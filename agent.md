Create a project to extract github comments from PR's from the last 3 months of a repo.

The project should be structured as follows:
>src
>>core
>>services
>>>github_service.py


github_service.py is responsible for interfacing with github and downloading historical PR's.
The function accepts the time in days that we want to go back to.

The things we care about fetching are, the diff and the pr comments along with any other metadata that you think is relevant.

I want to be able to save this as .json file per PR.


Further, make a function that takes in the repository name, installation token
The Repository_name, installation_id to first gain access to the github_repo and create an installation token.
default_value_repo="runwayml/api", default_installation_id=64320692

Some things in .env file:
# Runway x Tanagram App - for backtesting
INFISICAL_CLIENT_ID=1bb6bd5e-b258-4c20-a905-595d908b6d23
INFISICAL_CLIENT_SECRET=3c2ed5d522844c59788b0f747affc135fae0cbe0c1f6ccb2c786de261eb0ef00
INFISICAL_PROJECT_ID=28508ea4-b9cf-4c50-b42d-5ebcc49348eb

Make an AppConfig.py:
```
import contextvars
import os
from dotenv import load_dotenv
from pathlib import Path
from infisical_sdk import InfisicalSDKClient
import logging

from src.config.LoggingConfig import configure_logging

# override=True for not fetching from cache
load_dotenv(override=True)
configure_logging()
logger = logging.getLogger(__name__)


ENVIRONMENT = os.getenv("ENVIRONMENT")



# Initialize the client
client = InfisicalSDKClient(host="https://app.infisical.com")
client.auth.universal_auth.login(
    client_id=os.getenv("INFISICAL_CLIENT_ID"), 
    client_secret=os.getenv("INFISICAL_CLIENT_SECRET")
)
INFISICAL_PROJECT_ID = os.getenv("INFISICAL_PROJECT_ID")

def get_secret(secret_name: str, default_value: str = "") -> str:
    try:
        if client:
            return client.secrets.get_secret_by_name(
                secret_name=secret_name,
                project_id=INFISICAL_PROJECT_ID,
                environment_slug=ENVIRONMENT,
                secret_path="/",
            ).secretValue
    except Exception as e:
        logger.warning(f"Failed to fetch {secret_name} from secrets: {e}")
        return os.getenv(secret_name, default_value)


DEPLOYMENT_ENVIRONMENT = get_secret("DEPLOYMENT_ENVIRONMENT", "unknown")


# Base directory
BASE_DIR = Path(__file__).resolve().parent.parent.parent


# GitHub App configuration
GITHUB_APP_ID = get_secret("GITHUB_APP_ID")
GITHUB_APP_PRIVATE_KEY_PATH = get_secret("GITHUB_APP_PRIVATE_KEY_PATH")
GITHUB_APP_PRIVATE_KEY_PEM = get_secret("GITHUB_APP_PRIVATE_KEY_PEM")
GITHUB_APP_WEBHOOK_SECRET = get_secret("GITHUB_APP_WEBHOOK_SECRET")


# GitHub API
GITHUB_BASE_URL = "https://github.com"
GITHUB_API_BASE_URL = "https://api.github.com"
```